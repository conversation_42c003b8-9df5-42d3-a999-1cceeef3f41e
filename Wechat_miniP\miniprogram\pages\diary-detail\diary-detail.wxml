<!--diary-detail.wxml-->
<view class="page">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 日记内容 -->
  <view class="diary-detail" wx:if="{{!loading && diaryData}}">
    <!-- 头部信息 -->
    <view class="diary-header">
      <view class="diary-title">{{diaryData.title}}</view>
      <view class="diary-meta">
        <view class="diary-location" wx:if="{{diaryData.location}}">
          <text class="iconfont icon-location"></text>
          <text>{{diaryData.location}}</text>
        </view>
        <view class="diary-date">{{diaryData.created_at}}</view>
      </view>
      <view class="diary-stats">
        <view class="stat-item">
          <text class="iconfont icon-eye"></text>
          <text>{{diaryData.popularity || 0}}</text>
        </view>
        <view class="stat-item">
          <text class="iconfont icon-heart"></text>
          <text>{{diaryData.like_count || 0}}</text>
        </view>
      </view>
    </view>

    <!-- 图片展示 -->
    <view class="diary-images" wx:if="{{imageList.length > 0}}">
      <view class="image-grid {{imageList.length === 3 ? 'three-images' : ''}}">
        <view class="image-item" wx:for="{{imageList}}" wx:key="index" bindtap="previewImage" data-index="{{index}}">
          <image src="{{item}}" mode="aspectFill" />
        </view>
      </view>
    </view>

    <!-- 内容 -->
    <view class="diary-content">
      <text class="content-text">{{diaryData.content}}</text>
    </view>

    <!-- 标签 -->
    <view class="diary-tags" wx:if="{{tagList.length > 0}}">
      <view class="tag-item" wx:for="{{tagList}}" wx:key="index">{{item}}</view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="action-item" bindtap="toggleLike">
        <text class="iconfont {{isLiked ? 'icon-heart-fill' : 'icon-heart'}} {{isLiked ? 'liked' : ''}}"></text>
        <text class="action-text">{{isLiked ? '已点赞' : '点赞'}}</text>
      </view>
      <view class="action-item" bindtap="toggleFavorite">
        <text class="iconfont {{isFavorited ? 'icon-star-fill' : 'icon-star'}} {{isFavorited ? 'favorited' : ''}}"></text>
        <text class="action-text">{{isFavorited ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-item" bindtap="shareArticle">
        <text class="iconfont icon-share"></text>
        <text class="action-text">分享</text>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-state" wx:if="{{!loading && !diaryData}}">
    <view class="error-icon">😕</view>
    <view class="error-title">日记不存在</view>
    <view class="error-desc">该日记可能已被删除或不存在</view>
    <view class="error-action" bindtap="goBack">返回</view>
  </view>
</view>
