/* diary-detail.wxss */
.page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 日记详情 */
.diary-detail {
  background-color: white;
  min-height: 100vh;
}

/* 头部信息 */
.diary-header {
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.diary-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.diary-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.diary-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #666;
  font-size: 26rpx;
}

.diary-location .iconfont {
  font-size: 26rpx;
  color: #409EFF;
}

.diary-date {
  color: #999;
  font-size: 24rpx;
}

.diary-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 24rpx;
}

.stat-item .iconfont {
  font-size: 24rpx;
}

/* 图片展示 */
.diary-images {
  padding: 0 30rpx 30rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.image-item {
  width: calc(50% - 5rpx);
  height: 300rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.image-item:first-child:nth-last-child(1) {
  width: 100%;
  height: 400rpx;
}

/* 当有3张图片时的布局 */
.image-grid.three-images .image-item {
  width: calc(33.33% - 7rpx);
  height: 200rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
}

/* 内容 */
.diary-content {
  padding: 0 30rpx 30rpx;
}

.content-text {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  word-break: break-all;
}

/* 标签 */
.diary-tags {
  padding: 0 30rpx 30rpx;
}

.tag-item {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: #f0f0f0;
  color: #666;
  font-size: 24rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  background-color: white;
  position: sticky;
  bottom: 0;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 20rpx;
}

.action-item .iconfont {
  font-size: 40rpx;
  color: #666;
}

.action-item .iconfont.liked {
  color: #ff4757;
}

.action-item .iconfont.favorited {
  color: #ffa502;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 60rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.error-action {
  padding: 20rpx 40rpx;
  background-color: #409EFF;
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
}
