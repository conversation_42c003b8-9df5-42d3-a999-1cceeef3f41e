// place-search.ts
// 内嵌API服务，避免模块导入问题
const API_BASE_URL = 'http://10.129.241.148:5000/api';

// API服务类
class ApiService {
  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  // 构建查询字符串
  buildQueryString(params: Record<string, any>): string {
    const queryParts: string[] = [];
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    return queryParts.join('&');
  }

  // 搜索景点（从景点数据表）
  async searchLocations(params: { name?: string; type?: number; limit?: number }): Promise<LocationResult[]> {
    try {
      const queryParams: Record<string, any> = {};

      if (params.name) {
        queryParams.name = params.name;
      }
      if (params.type !== undefined) {
        queryParams.type = params.type;
      }
      if (params.limit) {
        queryParams.limit = params.limit;
      }

      const queryString = this.buildQueryString(queryParams);

      console.log(`搜索景点: ${this.baseUrl}/path/search-locations?${queryString}`);

      return new Promise((resolve) => {
        wx.request({
          url: `${this.baseUrl}/path/search-locations?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              resolve(locations);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('搜索景点失败:', error);
      return [];
    }
  }

  // 获取热门景点
  async getPopularLocations(params: { limit?: number; type?: number } = {}): Promise<LocationResult[]> {
    try {
      const queryParams: Record<string, any> = {
        limit: params.limit || 10
      };

      if (params.type !== undefined) {
        queryParams.type = params.type;
      }

      const queryString = this.buildQueryString(queryParams);

      console.log(`获取热门景点: ${this.baseUrl}/path/popular-locations?${queryString}`);

      return new Promise((resolve) => {
        wx.request({
          url: `${this.baseUrl}/path/popular-locations?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('热门景点响应:', res);
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              resolve(locations);
            } else {
              console.error('获取热门景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('获取热门景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('获取热门景点失败:', error);
      return [];
    }
  }

  // 按名称搜索景点（原有方法，搜索路径节点）
  async searchSpotsByName(params: { name: string; type?: string }): Promise<SpotResult[]> {
    try {
      const queryParams: Record<string, any> = {
        name: params.name
      };

      if (params.type) {
        queryParams.type = params.type;
      }

      const queryString = this.buildQueryString(queryParams);

      console.log(`搜索路径节点: ${this.baseUrl}/path/search-by-name?${queryString}`);

      return new Promise((resolve) => {
        wx.request({
          url: `${this.baseUrl}/path/search-by-name?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索路径节点响应:', res);
            if (res.statusCode === 200) {
              const spots = Array.isArray(res.data) ? res.data : [];
              resolve(spots);
            } else {
              console.error('搜索路径节点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索路径节点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('按名称搜索景点失败:', error);
      return [];
    }
  }

  // 获取地点建议
  async getLocationSuggestions(query: string, limit: number = 10): Promise<LocationSuggestion[]> {
    try {
      const queryString = this.buildQueryString({
        q: query,
        limit: limit
      });

      console.log(`获取地点建议: ${this.baseUrl}/path/location-suggestions?${queryString}`);

      return new Promise((resolve) => {
        wx.request({
          url: `${this.baseUrl}/path/location-suggestions?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('地点建议响应:', res);
            if (res.statusCode === 200) {
              const suggestions = Array.isArray(res.data) ? res.data : [];
              resolve(suggestions);
            } else {
              console.error('获取地点建议失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('获取地点建议请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('获取地点建议失败:', error);
      return [];
    }
  }

  // 通用请求方法
  async request<T>(options: { url: string; method?: string; data?: any }): Promise<{ data: T }> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}${options.url}`,
        method: options.method as any || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({ data: res.data as T });
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  // 按条件搜索景点
  async getSpotsByCriteria(params: any): Promise<{ nearby_spots: SpotResult[] }> {
    try {
      const response = await this.request<{ nearby_spots: SpotResult[] }>({
        url: '/path/search-by-criteria',
        method: 'POST',
        data: params
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('按条件搜索景点失败:', error);
      return { nearby_spots: [] };
    }
  }

  // 按起始位置搜索附近景点
  async searchSpotsByStart(params: any): Promise<{ nearby_spots: SpotResult[] }> {
    try {
      const response = await this.request<{ nearby_spots: SpotResult[] }>({
        url: '/path/search-by-start',
        method: 'POST',
        data: params
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('搜索附近景点失败:', error);
      return { nearby_spots: [] };
    }
  }
}

// 创建API服务实例
const apiService = new ApiService();

interface SpotResult {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
  path_distance?: number;
  description?: string;
}

interface LocationResult {
  location_id: number;
  name: string;
  type: number;
  keyword?: string;
  popularity: number;
  evaluation: number;
  image_url?: string;
  description?: string;
  address?: string;
}

interface LocationSuggestion {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
}

Component({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchMode: 'name', // 'name' | 'type' | 'nearby'

    // 起始位置
    startLocation: '',
    startVertexId: null as number | null,
    locationSuggestions: [] as LocationSuggestion[],
    showLocationSuggestions: false,

    // 搜索条件
    selectedType: '',
    searchDistance: 1000,

    // 结果
    searchResults: [] as SpotResult[],
    locationResults: [] as LocationResult[],
    popularLocations: [] as LocationResult[],
    loading: false,
    showPopular: true, // 是否显示热门景点

    // 类型选项
    typeOptions: [
      { label: '全部类型', value: '' },
      { label: '景点', value: '景点' },
      { label: '餐厅', value: '餐厅' },
      { label: '酒店', value: '酒店' },
      { label: '购物', value: '购物' },
      { label: '交通', value: '交通' },
      { label: '娱乐', value: '娱乐' },
      { label: '医疗', value: '医疗' },
      { label: '教育', value: '教育' }
    ],

    // 距离选项
    distanceOptions: [
      { label: '500米内', value: 500 },
      { label: '1公里内', value: 1000 },
      { label: '2公里内', value: 2000 },
      { label: '5公里内', value: 5000 },
      { label: '10公里内', value: 10000 }
    ]
  },

  pageLifetimes: {
    show() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1
        });
      }
    }
  },

  onLoad() {
    // 页面加载时测试API连接和加载热门景点
    this.testApiConnection();
    this.loadPopularLocations();
  },

  methods: {
    // 测试API连接
    async testApiConnection() {
      try {
        console.log('正在测试API连接...');
        const suggestions = await apiService.getLocationSuggestions('北京', 1);
        console.log('API连接测试成功:', suggestions);
      } catch (error) {
        console.error('API连接测试失败:', error);
        wx.showToast({
          title: 'API连接失败，请检查网络',
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 加载热门景点
    async loadPopularLocations() {
      try {
        console.log('正在加载热门景点...');
        const locations = await apiService.getPopularLocations({ limit: 10 });
        console.log('热门景点加载成功:', locations);
        this.setData({
          popularLocations: locations,
          showPopular: true
        });
      } catch (error) {
        console.error('加载热门景点失败:', error);
      }
    },

    // 搜索模式切换
    onSearchModeChange(e: any) {
      this.setData({
        searchMode: e.detail.value,
        searchResults: [],
        locationResults: [],
        showPopular: e.detail.value === 'name' && !this.data.searchKeyword // 只在名称搜索且无关键词时显示热门
      });
    },

    // 起始位置输入
    onStartLocationInput(e: any) {
      const value = e.detail.value;
      this.setData({ startLocation: value });

      if (value.trim()) {
        this.getLocationSuggestions(value);
      } else {
        this.setData({
          locationSuggestions: [],
          showLocationSuggestions: false
        });
      }
    },

    // 获取位置建议
    async getLocationSuggestions(query: string) {
      try {
        const suggestions = await apiService.getLocationSuggestions(query, 5);
        this.setData({
          locationSuggestions: suggestions,
          showLocationSuggestions: suggestions.length > 0
        });
      } catch (error) {
        console.error('获取位置建议失败:', error);
      }
    },

    // 选择起始位置
    onSelectStartLocation(e: any) {
      const index = e.currentTarget.dataset.index;
      const suggestion = this.data.locationSuggestions[index];

      this.setData({
        startLocation: suggestion.name,
        startVertexId: suggestion.vertex_id,
        showLocationSuggestions: false
      });
    },

    // 搜索关键词输入
    onSearchInput(e: any) {
      const keyword = e.detail.value;
      this.setData({
        searchKeyword: keyword,
        showPopular: !keyword && this.data.searchMode === 'name' // 有关键词时隐藏热门景点
      });
    },

    // 类型选择
    onTypeChange(e: any) {
      const index = e.detail.value;
      const selectedOption = this.data.typeOptions[index];
      this.setData({ selectedType: selectedOption.value });
    },

    // 距离选择
    onDistanceChange(e: any) {
      const index = e.detail.value;
      const selectedOption = this.data.distanceOptions[index];
      this.setData({ searchDistance: selectedOption.value });
    },

    // 执行搜索
    async handleSearch() {
      const { searchMode, searchKeyword, selectedType, startVertexId, searchDistance } = this.data;

      if (!searchKeyword.trim() && !selectedType && searchMode !== 'nearby') {
        wx.showToast({
          title: '请输入搜索条件',
          icon: 'none'
        });
        return;
      }

      if (searchMode === 'nearby' && !startVertexId) {
        wx.showToast({
          title: '请先选择起始位置',
          icon: 'none'
        });
        return;
      }

      this.setData({
        loading: true,
        showPopular: false // 搜索时隐藏热门景点
      });

      try {
        let results: SpotResult[] = [];
        let locationResults: LocationResult[] = [];

        if (searchMode === 'name') {
          // 按名称搜索 - 同时搜索景点数据表和路径节点
          const [locations, spots] = await Promise.all([
            apiService.searchLocations({
              name: searchKeyword,
              type: selectedType ? (selectedType === '景点' ? 1 : 0) : undefined,
              limit: 20
            }),
            apiService.searchSpotsByName({
              name: searchKeyword,
              type: selectedType
            })
          ]);

          locationResults = locations;
          results = spots;
        } else if (searchMode === 'type') {
          // 按类型搜索
          if (!startVertexId) {
            wx.showToast({
              title: '请先选择起始位置',
              icon: 'none'
            });
            return;
          }

          const response = await apiService.getSpotsByCriteria({
            startVertexId,
            distance: searchDistance,
            types: selectedType ? [selectedType] : [],
            keywords: searchKeyword ? [searchKeyword] : [],
            limit: 50
          });
          results = response.nearby_spots;
        } else if (searchMode === 'nearby') {
          // 附近搜索
          const response = await apiService.searchSpotsByStart({
            startVertexId: startVertexId!,
            distance: searchDistance,
            limit: 50
          });
          results = response.nearby_spots;
        }

        // 按距离排序
        if (results.length > 0 && results[0].path_distance !== undefined) {
          results.sort((a, b) => (a.path_distance || 0) - (b.path_distance || 0));
        }

        this.setData({
          searchResults: results,
          locationResults: locationResults
        });

        if (results.length === 0 && locationResults.length === 0) {
          wx.showToast({
            title: '未找到相关景点',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('搜索失败:', error);
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 查看景点详情
    onSpotTap(e: any) {
      const index = e.currentTarget.dataset.index;
      const spot = this.data.searchResults[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?id=${spot.vertex_id}&name=${spot.name}`
      });
    },

    // 查看景点详情（从景点数据表）
    onLocationTap(e: any) {
      const index = e.currentTarget.dataset.index;
      const location = this.data.locationResults[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?locationId=${location.location_id}&name=${location.name}`
      });
    },

    // 查看热门景点详情
    onPopularLocationTap(e: any) {
      const index = e.currentTarget.dataset.index;
      const location = this.data.popularLocations[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?locationId=${location.location_id}&name=${location.name}`
      });
    }
  }
})
