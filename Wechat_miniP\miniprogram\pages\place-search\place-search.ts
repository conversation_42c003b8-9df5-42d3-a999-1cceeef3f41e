// place-search.ts
const { apiService } = require('../../utils/api.js');

interface SpotResult {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
  path_distance?: number;
  description?: string;
}

interface LocationResult {
  location_id: number;
  name: string;
  type: number;
  keyword?: string;
  popularity: number;
  evaluation: number;
  image_url?: string;
  description?: string;
  address?: string;
}

interface LocationSuggestion {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
}

Component({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchMode: 'name', // 'name' | 'type' | 'nearby'

    // 起始位置
    startLocation: '',
    startVertexId: null as number | null,
    locationSuggestions: [] as LocationSuggestion[],
    showLocationSuggestions: false,

    // 搜索条件
    selectedType: '',
    searchDistance: 1000,

    // 结果
    searchResults: [] as SpotResult[],
    locationResults: [] as LocationResult[],
    popularLocations: [] as LocationResult[],
    loading: false,
    showPopular: true, // 是否显示热门景点

    // 类型选项
    typeOptions: [
      { label: '全部类型', value: '' },
      { label: '景点', value: '景点' },
      { label: '餐厅', value: '餐厅' },
      { label: '酒店', value: '酒店' },
      { label: '购物', value: '购物' },
      { label: '交通', value: '交通' },
      { label: '娱乐', value: '娱乐' },
      { label: '医疗', value: '医疗' },
      { label: '教育', value: '教育' }
    ],

    // 距离选项
    distanceOptions: [
      { label: '500米内', value: 500 },
      { label: '1公里内', value: 1000 },
      { label: '2公里内', value: 2000 },
      { label: '5公里内', value: 5000 },
      { label: '10公里内', value: 10000 }
    ]
  },

  pageLifetimes: {
    show() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1
        });
      }
    }
  },

  onLoad() {
    // 页面加载时测试API连接和加载热门景点
    this.testApiConnection();
    this.loadPopularLocations();
  },

  methods: {
    // 测试API连接
    async testApiConnection() {
      try {
        console.log('正在测试API连接...');
        const suggestions = await apiService.getLocationSuggestions('北京', 1);
        console.log('API连接测试成功:', suggestions);
      } catch (error) {
        console.error('API连接测试失败:', error);
        wx.showToast({
          title: 'API连接失败，请检查网络',
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 加载热门景点
    async loadPopularLocations() {
      try {
        console.log('正在加载热门景点...');
        const locations = await apiService.getPopularLocations({ limit: 10 });
        console.log('热门景点加载成功:', locations);
        this.setData({
          popularLocations: locations,
          showPopular: true
        });
      } catch (error) {
        console.error('加载热门景点失败:', error);
      }
    },

    // 搜索模式切换
    onSearchModeChange(e: any) {
      this.setData({
        searchMode: e.detail.value,
        searchResults: [],
        locationResults: [],
        showPopular: e.detail.value === 'name' && !this.data.searchKeyword // 只在名称搜索且无关键词时显示热门
      });
    },

    // 起始位置输入
    onStartLocationInput(e: any) {
      const value = e.detail.value;
      this.setData({ startLocation: value });

      if (value.trim()) {
        this.getLocationSuggestions(value);
      } else {
        this.setData({
          locationSuggestions: [],
          showLocationSuggestions: false
        });
      }
    },

    // 获取位置建议
    async getLocationSuggestions(query: string) {
      try {
        const suggestions = await apiService.getLocationSuggestions(query, 5);
        this.setData({
          locationSuggestions: suggestions,
          showLocationSuggestions: suggestions.length > 0
        });
      } catch (error) {
        console.error('获取位置建议失败:', error);
      }
    },

    // 选择起始位置
    onSelectStartLocation(e: any) {
      const index = e.currentTarget.dataset.index;
      const suggestion = this.data.locationSuggestions[index];

      this.setData({
        startLocation: suggestion.name,
        startVertexId: suggestion.vertex_id,
        showLocationSuggestions: false
      });
    },

    // 搜索关键词输入
    onSearchInput(e: any) {
      const keyword = e.detail.value;
      this.setData({
        searchKeyword: keyword,
        showPopular: !keyword && this.data.searchMode === 'name' // 有关键词时隐藏热门景点
      });
    },

    // 类型选择
    onTypeChange(e: any) {
      const index = e.detail.value;
      const selectedOption = this.data.typeOptions[index];
      this.setData({ selectedType: selectedOption.value });
    },

    // 距离选择
    onDistanceChange(e: any) {
      const index = e.detail.value;
      const selectedOption = this.data.distanceOptions[index];
      this.setData({ searchDistance: selectedOption.value });
    },

    // 执行搜索
    async handleSearch() {
      const { searchMode, searchKeyword, selectedType, startVertexId, searchDistance } = this.data;

      if (!searchKeyword.trim() && !selectedType && searchMode !== 'nearby') {
        wx.showToast({
          title: '请输入搜索条件',
          icon: 'none'
        });
        return;
      }

      if (searchMode === 'nearby' && !startVertexId) {
        wx.showToast({
          title: '请先选择起始位置',
          icon: 'none'
        });
        return;
      }

      this.setData({
        loading: true,
        showPopular: false // 搜索时隐藏热门景点
      });

      try {
        let results: SpotResult[] = [];
        let locationResults: LocationResult[] = [];

        if (searchMode === 'name') {
          // 按名称搜索 - 同时搜索景点数据表和路径节点
          const [locations, spots] = await Promise.all([
            apiService.searchLocations({
              name: searchKeyword,
              type: selectedType ? (selectedType === '景点' ? 1 : 0) : undefined,
              limit: 20
            }),
            apiService.searchSpotsByName({
              name: searchKeyword,
              type: selectedType
            })
          ]);

          locationResults = locations;
          results = spots;
        } else if (searchMode === 'type') {
          // 按类型搜索
          if (!startVertexId) {
            wx.showToast({
              title: '请先选择起始位置',
              icon: 'none'
            });
            return;
          }

          const response = await apiService.getSpotsByCriteria({
            startVertexId,
            distance: searchDistance,
            types: selectedType ? [selectedType] : [],
            keywords: searchKeyword ? [searchKeyword] : [],
            limit: 50
          });
          results = response.nearby_spots;
        } else if (searchMode === 'nearby') {
          // 附近搜索
          const response = await apiService.searchSpotsByStart({
            startVertexId: startVertexId!,
            distance: searchDistance,
            limit: 50
          });
          results = response.nearby_spots;
        }

        // 按距离排序
        if (results.length > 0 && results[0].path_distance !== undefined) {
          results.sort((a, b) => (a.path_distance || 0) - (b.path_distance || 0));
        }

        this.setData({
          searchResults: results,
          locationResults: locationResults
        });

        if (results.length === 0 && locationResults.length === 0) {
          wx.showToast({
            title: '未找到相关景点',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('搜索失败:', error);
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 查看景点详情
    onSpotTap(e: any) {
      const index = e.currentTarget.dataset.index;
      const spot = this.data.searchResults[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?id=${spot.vertex_id}&name=${spot.name}`
      });
    },

    // 查看景点详情（从景点数据表）
    onLocationTap(e: any) {
      const index = e.currentTarget.dataset.index;
      const location = this.data.locationResults[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?locationId=${location.location_id}&name=${location.name}`
      });
    },

    // 查看热门景点详情
    onPopularLocationTap(e: any) {
      const index = e.currentTarget.dataset.index;
      const location = this.data.popularLocations[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: `/pages/place-detail/place-detail?locationId=${location.location_id}&name=${location.name}`
      });
    }
  }
})
