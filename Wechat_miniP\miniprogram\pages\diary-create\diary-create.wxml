<!--diary-create.wxml-->
<view class="page">
  <view class="container">
    <!-- 标题输入 -->
    <view class="form-item">
      <view class="form-label">标题</view>
      <input class="form-input" placeholder="给你的旅行日记起个标题吧..." value="{{title}}" bindinput="onTitleInput" maxlength="50" />
    </view>
    
    <!-- 地点选择 -->
    <view class="form-item">
      <view class="form-label">地点</view>
      <view class="location-selector" bindtap="selectLocation">
        <text class="location-text {{location ? '' : 'placeholder'}}">{{location || '选择旅行地点'}}</text>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    
    <!-- 内容编辑 -->
    <view class="form-item">
      <view class="form-label">内容</view>
      <textarea class="content-textarea" placeholder="分享你的旅行故事..." value="{{content}}" bindinput="onContentInput" maxlength="2000" auto-height />
    </view>
    
    <!-- 图片上传 -->
    <view class="form-item">
      <view class="form-label">图片</view>
      <view class="image-upload">
        <view class="image-list">
          <view class="image-item" wx:for="{{imageList}}" wx:key="index">
            <image src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}" />
            <view class="image-delete" bindtap="deleteImage" data-index="{{index}}">
              <text class="iconfont icon-close"></text>
            </view>
          </view>
          <view class="image-add" wx:if="{{imageList.length < 6}}" bindtap="chooseImage">
            <text class="iconfont icon-add"></text>
            <text class="add-text">添加图片</text>
          </view>
        </view>
        <view class="image-tip">最多可添加6张图片</view>
      </view>
    </view>
    
    <!-- 标签 -->
    <view class="form-item">
      <view class="form-label">标签</view>
      <view class="tag-container">
        <view class="tag-list">
          <view class="tag-item {{item.selected ? 'selected' : ''}}" wx:for="{{tagOptions}}" wx:key="id" data-id="{{item.id}}" bindtap="toggleTag">
            {{item.name}}
          </view>
        </view>
        <view class="custom-tag">
          <input class="tag-input" placeholder="自定义标签" value="{{customTag}}" bindinput="onCustomTagInput" />
          <view class="tag-add-btn" bindtap="addCustomTag">添加</view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="btn-secondary" bindtap="saveDraft">保存草稿</view>
      <view class="btn-primary" bindtap="publishDiary">发布</view>
    </view>
  </view>
</view>
