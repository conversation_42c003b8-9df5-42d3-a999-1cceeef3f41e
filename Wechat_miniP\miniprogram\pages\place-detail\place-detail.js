// place-detail.js
Component({
  data: {
    placeId: 0,
    placeData: null,
    loading: true,
    isFavorited: false,
    relatedPlaces: []
  },

  lifetimes: {
    attached: function() {
      // 获取页面参数
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      
      if (options.id) {
        this.setData({
          placeId: parseInt(options.id)
        });
        this.loadPlaceDetail();
      } else {
        this.setData({ loading: false });
      }
    }
  },

  methods: {
    // 加载景点详情
    loadPlaceDetail: function() {
      const that = this;
      this.setData({ loading: true });

      // 模拟景点数据
      const mockData = {
        id: this.data.placeId,
        name: '故宫博物院',
        category: 'culture',
        rating: 4.8,
        reviews: 1250,
        image: '/images/assets/forbidden_city.jpg',
        description: '故宫博物院是中国明清两朝的皇家宫殿，旧称紫禁城，位于北京中轴线的中心。是世界上现存规模最大、保存最为完整的木质结构古建筑群。',
        address: '北京市东城区景山前街4号',
        openTime: '08:30-17:00',
        ticketPrice: '60元起',
        tags: ['历史', '文化', '古建筑', '世界遗产'],
        facilities: ['停车场', '餐厅', '纪念品店', '导览服务'],
        tips: [
          '建议提前网上预约门票',
          '参观时间建议3-4小时',
          '周一闭馆（法定节假日除外）',
          '禁止携带打火机等易燃物品'
        ]
      };

      setTimeout(function() {
        that.setData({
          placeData: mockData,
          loading: false
        });
        that.loadRelatedPlaces();
      }, 1000);
    },

    // 加载相关景点
    loadRelatedPlaces: function() {
      const relatedPlaces = [
        {
          id: 2,
          name: '天安门广场',
          image: '/images/assets/forbidden_city.jpg',
          rating: 4.7,
          distance: '0.5km'
        },
        {
          id: 3,
          name: '景山公园',
          image: '/images/assets/forbidden_city.jpg',
          rating: 4.6,
          distance: '0.8km'
        }
      ];

      this.setData({ relatedPlaces: relatedPlaces });
    },

    // 切换收藏状态
    toggleFavorite: function() {
      this.setData({
        isFavorited: !this.data.isFavorited
      });

      wx.showToast({
        title: this.data.isFavorited ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    },

    // 分享景点
    onShareAppMessage: function() {
      return {
        title: this.data.placeData ? this.data.placeData.name : '景点详情',
        path: '/pages/place-detail/place-detail?id=' + this.data.placeId
      };
    },

    // 查看图片
    previewImage: function() {
      if (this.data.placeData && this.data.placeData.image) {
        wx.previewImage({
          urls: [this.data.placeData.image]
        });
      }
    },

    // 导航到景点
    navigateToPlace: function() {
      wx.showToast({
        title: '导航功能开发中',
        icon: 'none'
      });
    },

    // 查看相关景点
    onRelatedPlaceTap: function(e) {
      const id = e.currentTarget.dataset.id;
      wx.redirectTo({
        url: '/pages/place-detail/place-detail?id=' + id
      });
    },

    // 返回
    goBack: function() {
      wx.navigateBack();
    }
  }
})
