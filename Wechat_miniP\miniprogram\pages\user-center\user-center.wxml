<!--user-center.wxml-->
<view class="page">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <view class="user-bg"></view>
    <view class="user-info">
      <view class="avatar-container" bindtap="getUserProfile">
        <image class="avatar" src="{{userInfo.avatar}}" mode="aspectFill" />
        <view class="avatar-edit">
          <text class="iconfont icon-camera"></text>
        </view>
      </view>
      <view class="user-details">
        <view class="user-name">{{userInfo.nickname}}</view>
        <view class="user-desc">记录美好旅行时光</view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-container">
      <view class="stat-item" bindtap="goToDiaryList">
        <view class="stat-number">{{userStats.diaryCount}}</view>
        <view class="stat-label">日记</view>
      </view>
      <view class="stat-item" bindtap="goToFavorites">
        <view class="stat-number">{{userStats.favoriteCount}}</view>
        <view class="stat-label">收藏</view>
      </view>
      <view class="stat-item" bindtap="goToRouteHistory">
        <view class="stat-number">{{userStats.routeCount}}</view>
        <view class="stat-label">路线</view>
      </view>
    </view>
  </view>

  <!-- 快捷功能 -->
  <view class="quick-actions">
    <view class="section-title">快捷功能</view>
    <view class="action-grid">
      <view class="action-item" wx:for="{{quickActions}}" wx:key="id" bindtap="onQuickActionTap" data-url="{{item.url}}">
        <view class="action-icon">{{item.icon}}</view>
        <view class="action-text">{{item.title}}</view>
      </view>
    </view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-section">
    <view class="menu-group" wx:for="{{menuGroups}}" wx:key="title">
      <view class="group-title">{{item.title}}</view>
      <view class="menu-list">
        <view class="menu-item" wx:for="{{item.items}}" wx:for-item="menuItem" wx:key="id" bindtap="onMenuItemTap" data-url="{{menuItem.url}}">
          <view class="menu-left">
            <view class="menu-icon">{{menuItem.icon}}</view>
            <view class="menu-content">
              <view class="menu-title">{{menuItem.title}}</view>
              <view class="menu-desc" wx:if="{{menuItem.desc}}">{{menuItem.desc}}</view>
            </view>
          </view>
          <view class="menu-right">
            <view class="menu-badge" wx:if="{{menuItem.badge}}">{{menuItem.badge}}</view>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <view class="version-text">鸿雁智游 v1.0.0</view>
  </view>
</view>
