/* diary-create.wxss */
.page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.container {
  padding: 30rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: white;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #eee;
}

/* 地点选择器 */
.location-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  background-color: white;
  border-radius: 10rpx;
  padding: 0 20rpx;
  border: 1rpx solid #eee;
}

.location-text {
  font-size: 28rpx;
  color: #333;
}

.location-text.placeholder {
  color: #999;
}

.icon-arrow-right {
  font-size: 24rpx;
  color: #999;
}

/* 内容输入 */
.content-textarea {
  width: 100%;
  min-height: 200rpx;
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #eee;
  line-height: 1.6;
}

/* 图片上传 */
.image-upload {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  border: 1rpx solid #eee;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-delete .iconfont {
  font-size: 24rpx;
  color: white;
}

.image-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.image-add .iconfont {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 标签容器 */
.tag-container {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  border: 1rpx solid #eee;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.tag-item {
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  border: 1rpx solid transparent;
}

.tag-item.selected {
  background-color: #e6f3ff;
  color: #409EFF;
  border-color: #409EFF;
}

.custom-tag {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.tag-input {
  flex: 1;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
}

.tag-add-btn {
  padding: 10rpx 20rpx;
  background-color: #409EFF;
  color: white;
  border-radius: 30rpx;
  font-size: 26rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 60rpx;
  padding-bottom: 40rpx;
}

.btn-secondary, .btn-primary {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

.btn-primary {
  background-color: #409EFF;
  color: white;
}
