// place-search.js
const { getApiBaseUrl } = require('../../config/api.ts');

Component({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchMode: 'name', // 'name' | 'type' | 'nearby'

    // 起始位置
    startLocation: '',
    startVertexId: null,
    locationSuggestions: [],
    showLocationSuggestions: false,

    // 搜索条件
    selectedType: '',
    selectedTypeIndex: 0,
    searchDistance: 1000,
    selectedDistanceIndex: 1,

    // 结果
    searchResults: [],
    loading: false,

    // 类型选项 - 与后端API对应的类型值
    typeOptions: [
      { label: '全部类型', value: '' },
      { label: '景点', value: '3' },
      { label: '餐厅', value: '4' },
      { label: '酒店', value: '5' },
      { label: '购物', value: '6' },
      { label: '交通', value: '7' },
      { label: '娱乐', value: '8' },
      { label: '医疗', value: '9' },
      { label: '教育', value: '10' }
    ],

    // 距离选项
    distanceOptions: [
      { label: '500米内', value: 500 },
      { label: '1公里内', value: 1000 },
      { label: '2公里内', value: 2000 },
      { label: '5公里内', value: 5000 },
      { label: '10公里内', value: 10000 }
    ]
  },

  pageLifetimes: {
    show: function() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1
        });
      }
    }
  },

  onLoad: function() {
    // 页面加载时测试API连接
    this.testApiConnection();
  },

  methods: {
    // 测试API连接
    testApiConnection: function() {
      console.log('API连接测试 - JavaScript版本');
    },

    // 搜索模式切换
    onSearchModeChange: function(e) {
      this.setData({
        searchMode: e.detail.value,
        searchResults: []
      });
    },

    // 起始位置输入
    onStartLocationInput: function(e) {
      const value = e.detail.value;
      this.setData({ startLocation: value });

      if (value.trim()) {
        this.getLocationSuggestions(value);
      } else {
        this.setData({
          locationSuggestions: [],
          showLocationSuggestions: false
        });
      }
    },

    // 获取位置建议
    getLocationSuggestions: function(query) {
      const that = this;

      // 调用后端API获取位置建议
      wx.request({
        url: `${getApiBaseUrl()}/path/location-suggestions`,
        method: 'GET',
        data: {
          q: query,
          limit: 10
        },
        success: function(res) {
          console.log('位置建议API响应:', res.data);
          if (res.statusCode === 200 && Array.isArray(res.data)) {
            that.setData({
              locationSuggestions: res.data.map(item => ({
                vertex_id: item.vertex_id,
                name: item.label || item.name,
                type: that.getTypeLabel(item.type)
              })),
              showLocationSuggestions: true
            });
          } else {
            console.error('位置建议API返回格式错误:', res.data);
            that.setData({
              locationSuggestions: [],
              showLocationSuggestions: false
            });
          }
        },
        fail: function(error) {
          console.error('获取位置建议失败:', error);
          that.setData({
            locationSuggestions: [],
            showLocationSuggestions: false
          });
        }
      });
    },

    // 获取类型标签
    getTypeLabel: function(typeValue) {
      const typeMap = {
        1: '起点',
        2: '终点',
        3: '景点',
        4: '餐厅',
        5: '酒店',
        6: '购物',
        7: '交通',
        8: '娱乐',
        9: '医疗',
        10: '教育'
      };
      return typeMap[typeValue] || '未知';
    },

    // 选择起始位置
    onSelectStartLocation: function(e) {
      const index = e.currentTarget.dataset.index;
      const suggestion = this.data.locationSuggestions[index];

      this.setData({
        startLocation: suggestion.name,
        startVertexId: suggestion.vertex_id,
        showLocationSuggestions: false,
        locationSuggestions: []
      });

      console.log('选择起始位置:', suggestion.name, 'ID:', suggestion.vertex_id);
    },

    // 搜索关键词输入
    onSearchInput: function(e) {
      this.setData({ searchKeyword: e.detail.value });
    },

    // 类型选择
    onTypeChange: function(e) {
      const index = e.detail.value;
      const selectedOption = this.data.typeOptions[index];
      this.setData({
        selectedType: selectedOption.value,
        selectedTypeIndex: index
      });
      console.log('选择类型:', selectedOption.label, '值:', selectedOption.value);
    },

    // 距离选择
    onDistanceChange: function(e) {
      const index = e.detail.value;
      const selectedOption = this.data.distanceOptions[index];
      this.setData({
        searchDistance: selectedOption.value,
        selectedDistanceIndex: index
      });
      console.log('选择距离:', selectedOption.label, '值:', selectedOption.value);
    },

    // 执行搜索
    handleSearch: function() {
      const searchKeyword = this.data.searchKeyword;
      const selectedType = this.data.selectedType;
      const searchMode = this.data.searchMode;
      const startVertexId = this.data.startVertexId;
      const searchDistance = this.data.searchDistance;

      console.log('开始搜索:', {
        searchMode,
        searchKeyword,
        selectedType,
        startVertexId,
        searchDistance
      });

      // 验证搜索条件
      if (searchMode === 'name' && !searchKeyword.trim()) {
        wx.showToast({
          title: '请输入景点名称',
          icon: 'none'
        });
        return;
      }

      if ((searchMode === 'type' || searchMode === 'nearby') && !startVertexId) {
        wx.showToast({
          title: '请先选择起始位置',
          icon: 'none'
        });
        return;
      }

      if (searchMode === 'type' && !selectedType) {
        wx.showToast({
          title: '请选择场所类型',
          icon: 'none'
        });
        return;
      }

      this.setData({ loading: true });

      // 根据搜索模式调用不同的API
      if (searchMode === 'name') {
        this.searchByName(searchKeyword);
      } else if (searchMode === 'type') {
        this.searchByType(startVertexId, selectedType, searchDistance, searchKeyword);
      } else if (searchMode === 'nearby') {
        this.searchNearby(startVertexId, searchDistance);
      }
    },

    // 按名称搜索
    searchByName: function(name) {
      const that = this;

      wx.request({
        url: `${getApiBaseUrl()}/path/search-by-name`,
        method: 'GET',
        data: {
          name: name
        },
        success: function(res) {
          console.log('按名称搜索结果:', res.data);
          if (res.statusCode === 200 && Array.isArray(res.data)) {
            const results = res.data.map(item => ({
              vertex_id: item.vertex_id,
              name: item.name,
              type: that.getTypeLabel(item.type),
              x: item.x,
              y: item.y,
              path_distance: item.distance
            }));

            that.setData({
              searchResults: results,
              loading: false
            });

            if (results.length === 0) {
              wx.showToast({
                title: '未找到相关景点',
                icon: 'none'
              });
            }
          } else {
            that.handleSearchError('搜索结果格式错误');
          }
        },
        fail: function(error) {
          console.error('按名称搜索失败:', error);
          that.handleSearchError('网络错误，请重试');
        }
      });
    },

    // 按类型搜索
    searchByType: function(startVertexId, type, distance, keyword) {
      const that = this;

      const requestData = {
        start_vertex_id: startVertexId,
        distance: distance,
        types: type ? [type] : [],
        keywords: keyword ? [keyword] : [],
        limit: 50
      };

      console.log('按类型搜索请求参数:', requestData);

      wx.request({
        url: `${getApiBaseUrl()}/path/spots-by-criteria`,
        method: 'POST',
        data: requestData,
        success: function(res) {
          console.log('按类型搜索结果:', res.data);
          if (res.statusCode === 200 && res.data.nearby_spots) {
            const results = res.data.nearby_spots
              .filter(spot => spot.path_distance <= distance)
              .sort((a, b) => a.path_distance - b.path_distance)
              .map(item => ({
                vertex_id: item.vertex_id,
                name: item.name,
                type: that.getTypeLabel(item.type),
                x: item.x,
                y: item.y,
                path_distance: item.path_distance
              }));

            that.setData({
              searchResults: results,
              loading: false
            });

            if (results.length === 0) {
              wx.showToast({
                title: '未找到符合条件的景点',
                icon: 'none'
              });
            }
          } else {
            that.handleSearchError('搜索结果格式错误');
          }
        },
        fail: function(error) {
          console.error('按类型搜索失败:', error);
          that.handleSearchError('网络错误，请重试');
        }
      });
    },

    // 附近搜索
    searchNearby: function(startVertexId, distance) {
      const that = this;

      const requestData = {
        start_vertex_id: startVertexId,
        distance: distance,
        limit: 50
      };

      console.log('附近搜索请求参数:', requestData);

      wx.request({
        url: `${getApiBaseUrl()}/path/spots-by-start`,
        method: 'POST',
        data: requestData,
        success: function(res) {
          console.log('附近搜索结果:', res.data);
          if (res.statusCode === 200 && res.data.nearby_spots) {
            const results = res.data.nearby_spots
              .filter(spot => spot.path_distance <= distance)
              .sort((a, b) => a.path_distance - b.path_distance)
              .map(item => ({
                vertex_id: item.vertex_id,
                name: item.name,
                type: that.getTypeLabel(item.type),
                x: item.x,
                y: item.y,
                path_distance: item.path_distance
              }));

            that.setData({
              searchResults: results,
              loading: false
            });

            if (results.length === 0) {
              wx.showToast({
                title: '附近没有找到景点',
                icon: 'none'
              });
            }
          } else {
            that.handleSearchError('搜索结果格式错误');
          }
        },
        fail: function(error) {
          console.error('附近搜索失败:', error);
          that.handleSearchError('网络错误，请重试');
        }
      });
    },

    // 处理搜索错误
    handleSearchError: function(message) {
      this.setData({ loading: false });
      wx.showToast({
        title: message,
        icon: 'none'
      });
    },

    // 查看景点详情
    onSpotTap: function(e) {
      const index = e.currentTarget.dataset.index;
      const spot = this.data.searchResults[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: '/pages/place-detail/place-detail?id=' + spot.vertex_id + '&name=' + encodeURIComponent(spot.name)
      });
    }
  }
})
