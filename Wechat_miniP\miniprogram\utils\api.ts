// api.ts - 微信小程序API工具类
import { getApiBaseUrl } from '../config/api';

interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

interface LocationSuggestion {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
}

interface SearchSpotParams {
  name?: string;
  type?: string;
  startVertexId?: number;
  distance?: number;
  limit?: number;
}

interface SpotResult {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
  path_distance?: number;
  description?: string;
}

interface LocationResult {
  location_id: number;
  name: string;
  type: number;
  keyword?: string;
  popularity: number;
  evaluation: number;
  image_url?: string;
  description?: string;
  address?: string;
}

class ApiService {
  private baseUrl = getApiBaseUrl(); // 使用配置文件中的API地址

  /**
   * 发送HTTP请求
   */
  private request<T>(options: {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    header?: any;
  }): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data as ApiResponse<T>);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  /**
   * 获取地点建议（自动完成）
   */
  async getLocationSuggestions(query: string, limit: number = 10): Promise<LocationSuggestion[]> {
    try {
      const queryString = this.buildQueryString({
        q: query,
        limit: limit
      });

      console.log(`获取地点建议: ${this.baseUrl}/path/location-suggestions?${queryString}`);

      // 直接使用wx.request
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/location-suggestions?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('地点建议响应:', res);
            if (res.statusCode === 200) {
              const suggestions = Array.isArray(res.data) ? res.data : [];
              resolve(suggestions);
            } else {
              console.error('获取地点建议失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('获取地点建议请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('获取地点建议失败:', error);
      return [];
    }
  }

  /**
   * 构建查询字符串（微信小程序兼容版本）
   */
  private buildQueryString(params: Record<string, any>): string {
    const queryParts: string[] = [];
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    return queryParts.join('&');
  }

  /**
   * 按名称搜索景点
   */
  async searchSpotsByName(params: { name: string; type?: string }): Promise<SpotResult[]> {
    try {
      const queryParams: Record<string, any> = {
        name: params.name
      };

      if (params.type) {
        queryParams.type = params.type;
      }

      const queryString = this.buildQueryString(queryParams);

      console.log(`搜索景点: ${this.baseUrl}/path/search-by-name?${queryString}`);

      // 直接使用wx.request，因为后端返回的是数组而不是包装的ApiResponse
      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/search-by-name?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            if (res.statusCode === 200) {
              // 后端直接返回数组，不需要解包data字段
              const spots = Array.isArray(res.data) ? res.data : [];
              resolve(spots);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('按名称搜索景点失败:', error);
      return [];
    }
  }

  /**
   * 多条件查询景点
   */
  async getSpotsByCriteria(params: {
    startVertexId?: number;
    locationName?: string;
    distance?: number;
    types?: string[];
    keywords?: string[];
    limit?: number;
  }): Promise<{ nearby_spots: SpotResult[] }> {
    try {
      const requestBody = {
        start_vertex_id: params.startVertexId,
        location_name: params.locationName,
        distance: params.distance || 1000,
        types: params.types || [],
        keywords: params.keywords || [],
        limit: params.limit || 15
      };
      
      const response = await this.request<{ nearby_spots: SpotResult[] }>({
        url: '/path/spots-by-criteria',
        method: 'POST',
        data: requestBody
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('多条件查询景点失败:', error);
      return { nearby_spots: [] };
    }
  }

  /**
   * 按起始点搜索附近景点
   */
  async searchSpotsByStart(params: {
    startVertexId: number;
    locationName?: string;
    distance?: number;
    limit?: number;
  }): Promise<{ nearby_spots: SpotResult[] }> {
    try {
      const requestBody = {
        start_vertex_id: params.startVertexId,
        location_name: params.locationName,
        distance: params.distance || 1000,
        limit: params.limit || 200,
        page: 1,
        per_page: 200
      };
      
      const response = await this.request<{ nearby_spots: SpotResult[] }>({
        url: '/path/spots-by-start',
        method: 'POST',
        data: requestBody
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('按起始点搜索景点失败:', error);
      return { nearby_spots: [] };
    }
  }

  /**
   * 搜索景点（从景点数据表）
   */
  async searchLocations(params: { name?: string; type?: number; limit?: number }): Promise<LocationResult[]> {
    try {
      const queryParams: Record<string, any> = {};

      if (params.name) {
        queryParams.name = params.name;
      }
      if (params.type !== undefined) {
        queryParams.type = params.type;
      }
      if (params.limit) {
        queryParams.limit = params.limit;
      }

      const queryString = this.buildQueryString(queryParams);

      console.log(`搜索景点: ${this.baseUrl}/path/search-locations?${queryString}`);

      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/search-locations?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              resolve(locations);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('搜索景点失败:', error);
      return [];
    }
  }

  /**
   * 获取热门景点
   */
  async getPopularLocations(params: { limit?: number; type?: number } = {}): Promise<LocationResult[]> {
    try {
      const queryParams: Record<string, any> = {
        limit: params.limit || 10
      };

      if (params.type !== undefined) {
        queryParams.type = params.type;
      }

      const queryString = this.buildQueryString(queryParams);

      console.log(`获取热门景点: ${this.baseUrl}/path/popular-locations?${queryString}`);

      return new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/path/popular-locations?${queryString}`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('热门景点响应:', res);
            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              resolve(locations);
            } else {
              console.error('获取热门景点失败:', res.statusCode, res.data);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('获取热门景点请求失败:', err);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('获取热门景点失败:', error);
      return [];
    }
  }

  /**
   * 获取热门景点推荐（保留原方法兼容性）
   */
  async getPopularSpots(limit: number = 20): Promise<SpotResult[]> {
    try {
      const response = await this.request<SpotResult[]>({
        url: '/recommend/popular',
        method: 'GET',
        data: { limit }
      });
      return response.data || [];
    } catch (error) {
      console.error('获取热门景点失败:', error);
      return [];
    }
  }
}

// 创建单例
const apiService = new ApiService();

// 微信小程序兼容的导出方式
module.exports = {
  apiService,
  default: apiService
};

// 导出类型（仅用于TypeScript类型检查）
export { SpotResult, LocationResult, LocationSuggestion, RouteResult, ApiResponse };
