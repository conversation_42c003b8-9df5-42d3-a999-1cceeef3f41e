/* place-search.wxss */

/* 页面基础样式 */
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.nav-bar {
  height: 88rpx;
  background-color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  z-index: 100;
}

.nav-bar-title {
  font-size: 36rpx;
  font-weight: bold;
}

.scrollarea {
  flex: 1;
  overflow-y: auto;
  padding-top: 20rpx;
  padding-bottom: calc(50px + env(safe-area-inset-bottom));
}

/* 通用样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

/* 搜索模式选择 */
.search-mode-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.radio-item radio {
  margin-right: 15rpx;
}

/* 位置选择 */
.location-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.input-container {
  position: relative;
}

.location-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.location-input:focus {
  border-color: #409EFF;
}

/* 建议列表 */
.suggestions-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 2rpx solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12rpx 12rpx;
  max-height: 400rpx;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f5f5f5;
}

.suggestion-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.suggestion-type {
  font-size: 24rpx;
  color: #666;
}

/* 搜索条件 */
.search-conditions {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.condition-item {
  margin-bottom: 30rpx;
}

.condition-item:last-child {
  margin-bottom: 0;
}

.condition-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.condition-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.condition-input:focus {
  border-color: #409EFF;
}

.condition-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 搜索按钮 */
.search-button-container {
  padding: 0 30rpx 30rpx;
}

.search-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(64, 158, 255, 0.3);
}

.search-button:disabled {
  opacity: 0.6;
}

.search-button:active {
  transform: scale(0.98);
}

/* 搜索结果 */
.results-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.results-list {
  padding: 0 30rpx 30rpx;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background-color: #f5f5f5;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.result-type {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.result-distance {
  font-size: 24rpx;
  color: #409EFF;
}

.result-arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
