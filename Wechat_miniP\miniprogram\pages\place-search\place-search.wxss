/* place-search.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: white;
  border-radius: 12rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 15rpx;
  font-size: 28rpx;
}

.clear-btn {
  background-color: #f56c6c;
  color: white;
  font-size: 24rpx;
  padding: 0 15rpx;
  height: 60rpx;
  line-height: 60rpx;
}

.search-status {
  padding: 20rpx;
  background-color: #e6f7ff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.status-text {
  font-size: 26rpx;
  color: #409EFF;
}

.section {
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-list {
  padding: 0 20rpx;
}

.location-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.location-item:last-child {
  border-bottom: none;
}

.location-item:active {
  background-color: #f5f5f5;
}

.location-info {
  width: 100%;
}

.location-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.location-meta {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 8rpx;
}

.location-type {
  font-size: 22rpx;
  color: #409EFF;
  background-color: #ecf5ff;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.location-popularity {
  font-size: 22rpx;
  color: #E6A23C;
}

.location-evaluation {
  font-size: 22rpx;
  color: #67C23A;
}

.location-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 5rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.location-address {
  font-size: 22rpx;
  color: #999;
}

.popular-item {
  border-left: 4rpx solid #ff6b6b;
  padding-left: 16rpx;
}

.result-item {
  border-left: 4rpx solid #409EFF;
  padding-left: 16rpx;
}

.empty-state, .default-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 20rpx;
  text-align: center;
}

.empty-icon, .default-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text, .default-text {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.retry-btn {
  background-color: #409EFF;
  color: white;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
}
