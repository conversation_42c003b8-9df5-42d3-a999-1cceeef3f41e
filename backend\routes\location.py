from flask import Blueprint, request, jsonify
from models.location import Location
from models.location_browse_count import LocationBrowseCount
from models.user import User
from models.location_browse import LocationBrowseHistory
from utils.database import db
from utils.response import success, error
from sqlalchemy import text
import time

location_bp = Blueprint('location', __name__)

@location_bp.route('', methods=['GET'])
def get_locations():
    """
    Get all locations
    """
    try:
        locations = Location.query.all()
        location_list = [location.to_dict() for location in locations]
        return success(location_list, 'Locations retrieved successfully')
    except Exception as e:
        return error(str(e))

@location_bp.route('/<int:location_id>', methods=['GET'])
def get_location(location_id):
    """
    Get location by ID
    """
    try:
        location = Location.query.get(location_id)
        if not location:
            return jsonify({'error': 'Location not found'}), 404
        return success(location.to_dict(), 'Location retrieved successfully')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@location_bp.route('/recommend/<int:user_id>', methods=['GET'])
def get_recommend_locations(user_id):
    """
    Get recommended locations for a user
    """
    try:
        # Get browse counts for this user
        browse_counts = LocationBrowseCount.query.filter_by(user_id=user_id).all()

        # Create user view counts dictionary
        user_view_counts = {bc.location_id: bc.count for bc in browse_counts}

        # Get all locations
        all_locations = Location.query.all()

        # If user has browse history, use location-based recommendation
        if user_view_counts:
            from utils.location_based_recommend import location_based_recommend
            recommended_locations = location_based_recommend(user_view_counts, all_locations)

            # Limit to 10 locations
            locations = recommended_locations[:10]
        else:
            # Get locations by popularity if no browse history
            locations = Location.query.order_by(Location.popularity.desc()).limit(10).all()

        return jsonify([location.to_dict() for location in locations]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@location_bp.route('/query', methods=['GET'])
def query_locations():
    """
    Query locations by name, type, and keyword - 使用自实现的高效查找算法
    完全不依赖SQL内置函数，使用多种自实现算法
    """
    try:
        # 获取查询参数
        name = request.args.get('name', '')
        type_val = request.args.get('type', '')
        keyword = request.args.get('keyword', '')
        sort_order = request.args.get('sortOrder', '0')

        print(f"=== 自实现地点查询API被调用 ===")
        print(f"查询参数: name='{name}', type='{type_val}', keyword='{keyword}', sortOrder='{sort_order}'")

        # 获取所有地点数据
        start_time = time.time()
        all_locations = Location.query.all()
        db_time = time.time() - start_time
        print(f"数据库查询耗时: {db_time:.3f}秒, 总地点数: {len(all_locations)}")

        # 使用自实现的高效查找算法
        from utils.location_finder import LocationFinder

        algorithm_start_time = time.time()

        # 构建查询词（优先使用name，其次keyword）
        query_text = name or keyword or ''
        print(f"实际查询词: '{query_text}'")

        # 第一步：模糊搜索匹配
        matched_locations = []
        if query_text:
            matched_locations = LocationFinder.fuzzy_search_locations(all_locations, query_text, limit=None)
            print(f"模糊搜索找到 {len(matched_locations)} 个匹配结果")

            # 显示前5个匹配结果的详细信息
            if matched_locations:
                print("前5个匹配结果:")
                for i, loc in enumerate(matched_locations[:5]):
                    score = LocationFinder._calculate_relevance_score(loc, query_text)
                    print(f"  {i+1}. {getattr(loc, 'name', 'N/A')} (评分: {score:.1f})")
        else:
            # 如果没有查询词，返回所有地点
            matched_locations = all_locations
            print("无查询词，返回所有地点")

        # 第二步：类型过滤（使用自实现的过滤算法）
        if type_val and type_val.isdigit():
            type_filter = int(type_val)
            print(f"应用类型过滤: {type_filter}")
            before_filter = len(matched_locations)
            matched_locations = LocationFinder._filter_by_type(matched_locations, type_filter)
            print(f"类型过滤后: {before_filter} -> {len(matched_locations)}")

        # 第三步：排序（使用自实现的排序算法）
        if sort_order and sort_order.isdigit():
            sort_int = int(sort_order)
            if sort_int == 0:  # 按人气排序
                print("应用热度排序")
                matched_locations = LocationFinder._quick_sort_by_popularity(matched_locations)
            elif sort_int == 1:  # 按评价排序
                print("应用评分排序")
                matched_locations = LocationFinder._quick_sort_by_evaluation(matched_locations)
        else:
            # 默认按人气排序
            print("应用默认热度排序")
            matched_locations = LocationFinder._quick_sort_by_popularity(matched_locations)

        algorithm_time = time.time() - algorithm_start_time
        print(f"自实现算法耗时: {algorithm_time:.3f}秒")
        print(f"最终结果数量: {len(matched_locations)}")

        # 转换为字典格式
        result = []
        for location in matched_locations:
            if hasattr(location, 'to_dict'):
                location_dict = location.to_dict()
            else:
                # 如果没有to_dict方法，手动构建字典
                location_dict = {
                    'location_id': getattr(location, 'location_id', None),
                    'name': getattr(location, 'name', ''),
                    'type': getattr(location, 'type', 0),
                    'keyword': getattr(location, 'keyword', ''),
                    'popularity': getattr(location, 'popularity', 0),
                    'evaluation': getattr(location, 'evaluation', 0)
                }
            result.append(location_dict)

        # 输出调试信息
        if result:
            print(f"返回结果示例:")
            for i, loc in enumerate(result[:3]):  # 只显示前3个
                print(f"  {i+1}. {loc.get('name', 'N/A')} (ID: {loc.get('location_id', 'N/A')})")
        else:
            print("未找到匹配结果")

        total_time = time.time() - start_time
        print(f"总耗时: {total_time:.3f}秒")
        print("=== 地点查询完成 ===\n")

        return jsonify(result), 200
    except Exception as e:
        import traceback
        traceback.print_exc()
        error_message = str(e)
        print(f"Error in query_locations: {error_message}")
        return jsonify({'error': error_message}), 500

@location_bp.route('/browse/<int:location_id>', methods=['POST'])
@location_bp.route('/browse', methods=['POST'])
def update_browse_count(location_id=None):
    """
    Update browse count for a location

    Can be called in two ways:
    1. POST /browse/<int:location_id> with user_id in request body
    2. POST /browse with user_id and location_id in request body
    """
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        # If location_id is not in URL, get it from request body
        if location_id is None:
            location_id = data.get('location_id')
            if not location_id:
                return jsonify({'error': 'Missing location_id parameter'}), 400

        if not user_id:
            return jsonify({'error': 'Missing user_id parameter'}), 400

        # Check if location and user exist
        location = Location.query.get(location_id)
        user = User.query.get(user_id)

        if not location:
            return jsonify({'error': 'Location not found'}), 404

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Update browse count
        browse_count = LocationBrowseCount.query.filter_by(
            location_id=location_id, user_id=user_id).first()

        if browse_count:
            browse_count.count += 1
        else:
            browse_count = LocationBrowseCount(
                location_id=location_id,
                user_id=user_id,
                count=1
            )
            db.session.add(browse_count)

        # Add browse history record
        browse_history = LocationBrowseHistory(
            user_id=user_id,
            location_id=location_id
        )
        db.session.add(browse_history)

        # Update location popularity
        location.popularity += 1

        db.session.commit()

        # 强制刷新推荐数据缓存
        try:
            from utils.data_manager import DataManager
            data_manager = DataManager()
            data_manager.refresh_data(force=True)
            print(f"景点 {location_id} 浏览计数更新后，已强制刷新推荐数据")
        except Exception as refresh_error:
            print(f"刷新推荐数据失败: {refresh_error}")

        return success({}, 'Browse count updated successfully')
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@location_bp.route('/browse/<int:location_id>', methods=['GET'])
def get_browse_count(location_id):
    """
    Get browse count for a location
    """
    try:
        # Sum all browse counts for this location
        total_count = db.session.query(db.func.sum(LocationBrowseCount.count)).filter(
            LocationBrowseCount.location_id == location_id).scalar() or 0

        return jsonify({'count': total_count}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 路由已合并到 /query 中，通过 keyword 参数搜索地点

# 路由已合并到 /browse/<int:location_id> 中，通过请求体参数获取 user_id

@location_bp.route('/fuzzy_search', methods=['GET'])
def fuzzy_search_locations():
    """
    地点模糊查询 - 使用自实现的高效模糊查找算法
    完全不依赖SQL内置函数，使用多种自实现算法
    """
    try:
        query = request.args.get('query', '').strip()
        limit = int(request.args.get('limit', 10))

        print(f"=== 自实现模糊搜索API被调用 ===")
        print(f"查询词: '{query}', 限制数量: {limit}")

        # 获取所有地点数据
        start_time = time.time()
        all_locations = Location.query.all()
        db_time = time.time() - start_time
        print(f"数据库查询耗时: {db_time:.3f}秒, 总地点数: {len(all_locations)}")

        # 使用自实现的高效模糊查找算法
        from utils.location_finder import LocationFinder

        algorithm_start_time = time.time()
        matched_locations = LocationFinder.fuzzy_search_locations(all_locations, query, limit)
        algorithm_time = time.time() - algorithm_start_time

        print(f"自实现算法耗时: {algorithm_time:.3f}秒")
        print(f"匹配结果数量: {len(matched_locations)}")

        # 转换为字典格式
        result = []
        for location in matched_locations:
            location_dict = location.to_dict()
            result.append(location_dict)

        # 输出调试信息
        if result:
            print(f"返回结果示例:")
            for i, loc in enumerate(result[:3]):  # 只显示前3个
                print(f"  {i+1}. {loc.get('name', 'N/A')} (ID: {loc.get('location_id', 'N/A')})")
        else:
            print("未找到匹配结果")

        total_time = time.time() - start_time
        print(f"总耗时: {total_time:.3f}秒")
        print("=== 模糊搜索完成 ===\n")

        return success(result, f'自实现模糊搜索完成，找到 {len(result)} 个结果')

    except Exception as e:
        print(f"模糊搜索出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return error(f'Error in fuzzy search: {str(e)}')
