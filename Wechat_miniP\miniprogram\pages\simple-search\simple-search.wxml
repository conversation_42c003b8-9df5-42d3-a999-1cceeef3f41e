<!--simple-search.wxml-->
<view class="container">
  <view class="header">
    <text class="title">景点搜索测试</text>
    <button class="test-btn" bindtap="testConnection" size="mini">测试连接</button>
  </view>

  <!-- 搜索框 -->
  <view class="search-box">
    <input 
      class="search-input" 
      placeholder="输入景点名称，如：故宫、天安门"
      value="{{searchKeyword}}"
      bindinput="onSearchInput"
    />
    <button class="search-btn" bindtap="handleSearch" loading="{{loading}}">
      {{loading ? '搜索中' : '搜索'}}
    </button>
    <button class="clear-btn" bindtap="onClearSearch" wx:if="{{searchKeyword}}">
      清空
    </button>
  </view>

  <!-- 热门景点 -->
  <view class="section" wx:if="{{showPopular && popularLocations.length > 0}}">
    <view class="section-title">🔥 热门景点推荐</view>
    <view class="location-list">
      <view 
        class="location-item popular-item"
        wx:for="{{popularLocations}}"
        wx:key="location_id"
        data-index="{{index}}"
        bindtap="onPopularTap"
      >
        <view class="location-info">
          <view class="location-name">{{item.name}}</view>
          <view class="location-meta">
            <text class="location-type">{{item.type === 1 ? '景点' : '学校'}}</text>
            <text class="location-popularity">热度: {{item.popularity}}</text>
            <text class="location-evaluation">评分: {{item.evaluation}}</text>
          </view>
          <view class="location-description" wx:if="{{item.description}}">
            {{item.description}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="section" wx:if="{{searchResults.length > 0}}">
    <view class="section-title">搜索结果 ({{searchResults.length}})</view>
    <view class="location-list">
      <view 
        class="location-item result-item"
        wx:for="{{searchResults}}"
        wx:key="location_id"
        data-index="{{index}}"
        bindtap="onResultTap"
      >
        <view class="location-info">
          <view class="location-name">{{item.name}}</view>
          <view class="location-meta">
            <text class="location-type">{{item.type === 1 ? '景点' : '学校'}}</text>
            <text class="location-popularity">热度: {{item.popularity}}</text>
            <text class="location-evaluation">评分: {{item.evaluation}}</text>
          </view>
          <view class="location-description" wx:if="{{item.description}}">
            {{item.description}}
          </view>
          <view class="location-address" wx:if="{{item.address}}">
            📍 {{item.address}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && !showPopular && searchResults.length === 0}}">
    <text class="empty-icon">🔍</text>
    <text class="empty-text">未找到相关景点，请尝试其他关键词</text>
  </view>

  <!-- 默认状态 -->
  <view class="default-state" wx:if="{{!loading && showPopular && popularLocations.length === 0}}">
    <text class="default-icon">🏞️</text>
    <text class="default-text">正在加载热门景点...</text>
  </view>

  <!-- 调试信息 -->
  <view class="debug-info">
    <text class="debug-text">API地址: {{API_BASE_URL || 'http://**************:5000/api'}}</text>
    <text class="debug-text">热门景点数量: {{popularLocations.length}}</text>
    <text class="debug-text">搜索结果数量: {{searchResults.length}}</text>
  </view>
</view>
