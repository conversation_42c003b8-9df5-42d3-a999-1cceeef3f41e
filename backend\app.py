import os
import logging
from logging.handlers import RotatingFileHand<PERSON>
from flask import Flask, jsonify, send_from_directory, request
from flask_cors import CORS
try:
    # 尝试从原始config.py导入
    from config import Config
except ImportError:
    # 如果失败，从config包导入
    from config import Config
from utils.database import db
from routes.auth import auth_bp
from routes.user import user_bp
from flask_migrate import Migrate
from utils.response import server_error
# 蓝图注册
from routes.ai_generator import ai_bp
from routes.location import location_bp
from routes.path import path_bp
from routes.article import article_bp
from routes.article_score import article_score_bp
from routes.upload import upload_bp
from routes.favorites import favorites_bp
from routes.location_favorites import location_favorites_bp
from routes.recommend import recommend_bp
from routes.advanced_recommend import advanced_recommend_bp
from routes.location_rating import location_rating_bp
from routes.location_favorite import location_favorite_bp
from routes.article_comment import article_comment_bp
from routes.article_like import article_like_bp
from routes.food import food_bp
from routes.restaurant_user import restaurant_user_bp
from routes.cart import cart_bp
from routes.music import music_bp

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    app.config['UPLOAD_FOLDER'] = os.path.join(BASE_DIR, 'uploads')
    # 文件上传配置
    # app.config['UPLOAD_FOLDER'] = 'uploads'
    # app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB限制

    # 确保上传目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # 确保locations目录存在
    locations_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'locations')
    os.makedirs(locations_dir, exist_ok=True)

    # 确保其他必要的目录存在
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'avatars'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'images'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'videos'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'animations'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'music'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'temp'), exist_ok=True)

    # 确保餐馆相关目录存在
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'restaurants'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'restaurants', 'dishes'), exist_ok=True)
    # 增强CORS配置 - 支持小程序和网页端
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:8080", "http://127.0.0.1:8080", "*"],  # 支持所有来源（开发环境）
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Accept"],
            "supports_credentials": True,
            "expose_headers": ["Content-Type", "Authorization"]
        }
    })

    # 设置日志 - 使用绝对路径
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
    if not os.path.exists(LOG_DIR):
        os.mkdir(LOG_DIR)

    LOG_FILE = os.path.join(LOG_DIR, 'app.log')
    file_handler = RotatingFileHandler(LOG_FILE, maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info(f'应用启动，日志文件位置: {LOG_FILE}')

    # 同时输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'
    ))
    console_handler.setLevel(logging.INFO)
    app.logger.addHandler(console_handler)

    # 添加请求日志中间件
    @app.before_request
    def log_request_info():
        print(f"收到请求: {request.method} {request.path}")
        print(f"查询参数: {dict(request.args)}")
        # 只有在确实有JSON数据时才尝试解析
        if request.is_json and request.content_length and request.content_length > 0:
            try:
                json_data = request.get_json()
                print(f"JSON数据: {json_data}")
            except Exception as e:
                print(f"JSON解析失败: {str(e)}")
        elif request.form:
            print(f"表单数据: {dict(request.form)}")

    # 初始化数据库
    db.init_app(app)
    migrate = Migrate(app, db)  # 确保这行在db.init_app(app)之后

    with app.app_context():
        db.create_all()

    # 注册蓝图
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(user_bp, url_prefix='/api/user')
    app.register_blueprint(ai_bp, url_prefix='/api/ai')
    app.register_blueprint(location_bp, url_prefix='/api/locations')
    app.register_blueprint(path_bp, url_prefix='/api/path')
    app.register_blueprint(article_bp, url_prefix='/api/articles')
    app.register_blueprint(article_score_bp, url_prefix='/api/article_score')
    app.register_blueprint(upload_bp, url_prefix='/api/upload')
    # 注册收藏相关蓝图
    app.register_blueprint(favorites_bp, url_prefix='/api/favorites')
    app.register_blueprint(location_favorites_bp, url_prefix='/api/location-favorites')
    app.register_blueprint(recommend_bp, url_prefix='/api/recommend')
    app.register_blueprint(advanced_recommend_bp, url_prefix='/api/advanced-recommend')
    app.register_blueprint(location_rating_bp, url_prefix='/api/locations')
    app.register_blueprint(location_favorite_bp, url_prefix='/api/location_favorite')
    app.register_blueprint(article_comment_bp, url_prefix='/api/article_comment')
    app.register_blueprint(article_like_bp, url_prefix='/api/article_like')
    app.register_blueprint(food_bp, url_prefix='/api/food')
    app.register_blueprint(restaurant_user_bp, url_prefix='/api/restaurant')
    app.register_blueprint(cart_bp, url_prefix='/api/cart')
    app.register_blueprint(music_bp, url_prefix='/api/music')

    # 注册全局错误处理器
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'code': 404,
            'message': 'Resource not found',
            'data': None
        }), 404

    @app.errorhandler(500)
    def internal_error(error):
        return server_error('Internal server error')

    @app.errorhandler(Exception)
    def handle_exception(error):
        return server_error(str(error))

    # 添加静态文件路由
    @app.route('/static/<path:filename>')
    def serve_static(filename):
        return send_from_directory(os.path.join(app.root_path, 'static'), filename)

    # 添加上传文件路由
    @app.route('/uploads/<path:filename>')
    def serve_uploads(filename):
        return send_from_directory(os.path.join(app.root_path, 'uploads'), filename)

    return app

if __name__ == '__main__':
    app = create_app()
    # 绑定到所有接口，允许外部访问（包括小程序）
    app.run(host='0.0.0.0', port=5000, debug=True)
