// simple-search.js - 简化版搜索页面，用于测试API
const API_BASE_URL = 'http://10.129.241.148:5000/api';

Page({
  data: {
    searchKeyword: '',
    searchResults: [],
    popularLocations: [],
    loading: false,
    showPopular: true,
    debugInfo: {
      lastRequest: '',
      lastResponse: '',
      lastError: '',
      requestCount: 0
    }
  },

  onLoad() {
    console.log('简化搜索页面加载');
    this.loadPopularLocations();
  },

  // 构建查询字符串
  buildQueryString(params) {
    const queryParts = [];
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null && value !== '') {
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    return queryParts.join('&');
  },

  // 更新调试信息
  updateDebugInfo(type, data) {
    const debugInfo = this.data.debugInfo;
    debugInfo.requestCount++;

    if (type === 'request') {
      debugInfo.lastRequest = data;
    } else if (type === 'response') {
      debugInfo.lastResponse = JSON.stringify(data, null, 2);
    } else if (type === 'error') {
      debugInfo.lastError = data;
    }

    this.setData({ debugInfo });
  },

  // 加载热门景点
  async loadPopularLocations() {
    try {
      console.log('正在加载热门景点...');

      const queryString = this.buildQueryString({ limit: 10 });
      const url = `${API_BASE_URL}/path/popular-locations?${queryString}`;

      console.log('请求URL:', url);
      this.updateDebugInfo('request', url);

      wx.request({
        url: url,
        method: 'GET',
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('热门景点响应:', res);
          this.updateDebugInfo('response', res);

          if (res.statusCode === 200) {
            const locations = Array.isArray(res.data) ? res.data : [];
            this.setData({
              popularLocations: locations,
              showPopular: true
            });
            console.log('热门景点加载成功:', locations);

            if (locations.length === 0) {
              wx.showToast({
                title: '热门景点数据为空',
                icon: 'none'
              });
            }
          } else {
            console.error('获取热门景点失败:', res.statusCode, res.data);
            this.updateDebugInfo('error', `HTTP ${res.statusCode}: ${JSON.stringify(res.data)}`);
            wx.showToast({
              title: `请求失败: ${res.statusCode}`,
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('获取热门景点请求失败:', err);
          this.updateDebugInfo('error', `网络错误: ${JSON.stringify(err)}`);
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('加载热门景点失败:', error);
      this.updateDebugInfo('error', `异常: ${error.message}`);
    }
  },

  // 搜索景点
  async searchLocations(keyword) {
    try {
      console.log('正在搜索景点:', keyword);

      const queryString = this.buildQueryString({
        name: keyword,
        limit: 20
      });
      const url = `${API_BASE_URL}/path/search-locations?${queryString}`;

      console.log('搜索URL:', url);
      this.updateDebugInfo('request', url);

      return new Promise((resolve) => {
        wx.request({
          url: url,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            console.log('搜索景点响应:', res);
            this.updateDebugInfo('response', res);

            if (res.statusCode === 200) {
              const locations = Array.isArray(res.data) ? res.data : [];
              console.log('搜索结果解析:', locations);
              resolve(locations);
            } else {
              console.error('搜索景点失败:', res.statusCode, res.data);
              this.updateDebugInfo('error', `搜索失败 HTTP ${res.statusCode}: ${JSON.stringify(res.data)}`);
              resolve([]);
            }
          },
          fail: (err) => {
            console.error('搜索景点请求失败:', err);
            this.updateDebugInfo('error', `搜索网络错误: ${JSON.stringify(err)}`);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('搜索景点失败:', error);
      this.updateDebugInfo('error', `搜索异常: ${error.message}`);
      return [];
    }
  },

  // 输入框变化
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ 
      searchKeyword: keyword,
      showPopular: !keyword // 有关键词时隐藏热门景点
    });
  },

  // 执行搜索
  async handleSearch() {
    const keyword = this.data.searchKeyword.trim();
    
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    this.setData({ 
      loading: true,
      showPopular: false 
    });

    try {
      const results = await this.searchLocations(keyword);
      
      this.setData({
        searchResults: results
      });

      if (results.length === 0) {
        wx.showToast({
          title: '未找到相关景点',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: `找到${results.length}个结果`,
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('搜索失败:', error);
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 清空搜索
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      showPopular: true
    });
  },

  // 点击热门景点
  onPopularTap(e) {
    const index = e.currentTarget.dataset.index;
    const location = this.data.popularLocations[index];
    
    wx.showModal({
      title: location.name,
      content: `热度: ${location.popularity}\n评分: ${location.evaluation}\n${location.description || '暂无描述'}`,
      showCancel: false
    });
  },

  // 点击搜索结果
  onResultTap(e) {
    const index = e.currentTarget.dataset.index;
    const location = this.data.searchResults[index];
    
    wx.showModal({
      title: location.name,
      content: `热度: ${location.popularity}\n评分: ${location.evaluation}\n${location.description || '暂无描述'}`,
      showCancel: false
    });
  },

  // 测试网络连接
  testConnection() {
    wx.showLoading({ title: '测试连接...' });

    wx.request({
      url: `${API_BASE_URL}/path/popular-locations?limit=1`,
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        console.log('连接测试响应:', res);
        this.updateDebugInfo('response', res);

        if (res.statusCode === 200) {
          wx.showToast({
            title: '连接成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: `连接失败: ${res.statusCode}`,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('连接测试失败:', err);
        this.updateDebugInfo('error', `连接测试失败: ${JSON.stringify(err)}`);
        wx.showToast({
          title: '连接失败',
          icon: 'none'
        });
      }
    });
  },

  // 测试搜索API
  testSearchAPI() {
    wx.showLoading({ title: '测试搜索API...' });

    const testKeywords = ['故宫', '天安门', '北京'];
    let testIndex = 0;

    const runTest = () => {
      if (testIndex >= testKeywords.length) {
        wx.hideLoading();
        wx.showToast({
          title: '搜索API测试完成',
          icon: 'success'
        });
        return;
      }

      const keyword = testKeywords[testIndex];
      console.log(`测试搜索关键词: ${keyword}`);

      this.searchLocations(keyword).then(results => {
        console.log(`${keyword} 搜索结果:`, results);
        testIndex++;
        setTimeout(runTest, 1000); // 延迟1秒进行下一个测试
      });
    };

    runTest();
  },

  // 查看调试信息
  showDebugInfo() {
    const debug = this.data.debugInfo;
    const content = `请求次数: ${debug.requestCount}\n\n最后请求: ${debug.lastRequest}\n\n最后错误: ${debug.lastError || '无'}`;

    wx.showModal({
      title: '调试信息',
      content: content,
      showCancel: false
    });
  },

  // 查看完整响应
  showFullResponse() {
    const debug = this.data.debugInfo;

    wx.showModal({
      title: '最后响应',
      content: debug.lastResponse || '暂无响应数据',
      showCancel: false
    });
  }
});
