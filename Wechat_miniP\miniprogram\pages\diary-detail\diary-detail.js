// diary-detail.js
Component({
  data: {
    diaryId: 0,
    diaryData: null,
    loading: true,
    imageList: [],
    tagList: [],
    isLiked: false,
    isFavorited: false,
    userId: 1 // 临时用户ID
  },
  
  lifetimes: {
    attached: function() {
      // 获取页面参数
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      
      if (options.id) {
        this.setData({
          diaryId: parseInt(options.id)
        });
        this.loadDiaryDetail();
      } else {
        this.setData({ loading: false });
      }
    }
  },
  
  methods: {
    // 加载日记详情
    loadDiaryDetail: function() {
      const that = this;
      this.setData({ loading: true });
      
      wx.request({
        url: 'http://localhost:5000/api/articles/' + this.data.diaryId,
        method: 'GET',
        success: function(res) {
          if (res.data.success && res.data.data) {
            const diaryData = res.data.data;
            
            // 处理图片列表
            const imageList = [
              diaryData.image_url,
              diaryData.image_url_2,
              diaryData.image_url_3,
              diaryData.image_url_4,
              diaryData.image_url_5,
              diaryData.image_url_6
            ].filter(function(url) {
              return url && url.trim();
            });
            
            // 处理标签列表
            let tagList = [];
            if (diaryData.tags) {
              try {
                tagList = JSON.parse(diaryData.tags);
              } catch (e) {
                tagList = [];
              }
            }
            
            that.setData({
              diaryData: Object.assign({}, diaryData, {
                created_at: that.formatDate(diaryData.created_at)
              }),
              imageList: imageList,
              tagList: tagList,
              loading: false
            });
            
            // 检查点赞和收藏状态
            that.checkLikeStatus();
            that.checkFavoriteStatus();
          } else {
            that.setData({ loading: false });
          }
        },
        fail: function(error) {
          console.error('加载日记详情失败:', error);
          that.setData({ loading: false });
        }
      });
    },
    
    // 格式化日期
    formatDate: function(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
    },
    
    // 预览图片
    previewImage: function(e) {
      const index = e.currentTarget.dataset.index;
      wx.previewImage({
        current: this.data.imageList[index],
        urls: this.data.imageList
      });
    },
    
    // 检查点赞状态
    checkLikeStatus: function() {
      const that = this;
      wx.request({
        url: 'http://localhost:5000/api/article_like/check',
        method: 'POST',
        data: {
          user_id: this.data.userId,
          article_id: this.data.diaryId
        },
        success: function(res) {
          if (res.data.success) {
            that.setData({
              isLiked: res.data.data.is_liked || false
            });
          }
        },
        fail: function(error) {
          console.error('检查点赞状态失败:', error);
        }
      });
    },
    
    // 检查收藏状态
    checkFavoriteStatus: function() {
      // 这里可以添加检查收藏状态的逻辑
      // 暂时设为false
      this.setData({ isFavorited: false });
    },
    
    // 切换点赞
    toggleLike: function() {
      const that = this;
      const url = this.data.isLiked 
        ? 'http://localhost:5000/api/article_like/unlike'
        : 'http://localhost:5000/api/article_like';
      
      wx.request({
        url: url,
        method: 'POST',
        data: {
          user_id: this.data.userId,
          article_id: this.data.diaryId
        },
        success: function(res) {
          if (res.data.success) {
            that.setData({
              isLiked: !that.data.isLiked
            });
            
            // 更新点赞数
            if (that.data.diaryData) {
              const newLikeCount = that.data.isLiked 
                ? that.data.diaryData.like_count + 1
                : that.data.diaryData.like_count - 1;
              
              that.setData({
                'diaryData.like_count': Math.max(0, newLikeCount)
              });
            }
          }
        },
        fail: function(error) {
          console.error('点赞操作失败:', error);
          wx.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 切换收藏
    toggleFavorite: function() {
      this.setData({
        isFavorited: !this.data.isFavorited
      });
      
      wx.showToast({
        title: this.data.isFavorited ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    },
    
    // 分享文章
    shareArticle: function() {
      wx.showShareMenu({
        withShareTicket: true
      });
    },
    
    // 返回
    goBack: function() {
      wx.navigateBack();
    }
  }
});
