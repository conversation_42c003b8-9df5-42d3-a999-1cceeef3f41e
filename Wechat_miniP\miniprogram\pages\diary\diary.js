// diary.js
Component({
  data: {
    diaryList: [],
    loading: false,
    showSearch: false,
    searchKeyword: '',
    filterType: 'all', // all, recent, popular
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    userId: 1 // 临时用户ID，实际应该从全局状态获取
  },
  
  pageLifetimes: {
    show: function() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 3
        });
      }
      // 加载日记列表
      this.loadDiaryList(true);
    }
  },
  
  methods: {
    // 加载日记列表
    loadDiaryList: function(reset) {
      const that = this;
      if (reset === undefined) reset = false;
      
      if (this.data.loading) return;
      
      this.setData({ loading: true });
      
      const page = reset ? 1 : this.data.currentPage;
      
      wx.request({
        url: 'http://localhost:5000/api/articles',
        method: 'GET',
        data: {
          page: page,
          size: this.data.pageSize,
          user_id: this.data.userId
        },
        success: function(res) {
          if (res.data.success) {
            const newList = res.data.data.articles || [];
            const diaryList = reset ? newList : that.data.diaryList.concat(newList);
            
            that.setData({
              diaryList: diaryList.map(function(item) {
                return Object.assign({}, item, {
                  content_preview: that.getContentPreview(item.content || ''),
                  created_at: that.formatDate(item.created_at)
                });
              }),
              currentPage: page,
              hasMore: newList.length >= that.data.pageSize,
              loading: false
            });
          } else {
            wx.showToast({
              title: '加载失败',
              icon: 'none'
            });
            that.setData({ loading: false });
          }
        },
        fail: function(error) {
          console.error('加载日记列表失败:', error);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
          that.setData({ loading: false });
        }
      });
    },
    
    // 获取内容预览
    getContentPreview: function(content) {
      if (!content) return '';
      // 移除HTML标签并截取前100个字符
      const plainText = content.replace(/<[^>]*>/g, '');
      return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText;
    },
    
    // 格式化日期
    formatDate: function(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      
      if (days === 0) {
        return '今天';
      } else if (days === 1) {
        return '昨天';
      } else if (days < 7) {
        return days + '天前';
      } else {
        return (date.getMonth() + 1) + '月' + date.getDate() + '日';
      }
    },
    
    // 切换搜索栏显示
    toggleSearch: function() {
      this.setData({
        showSearch: !this.data.showSearch
      });
    },
    
    // 搜索输入
    onSearchInput: function(e) {
      this.setData({
        searchKeyword: e.detail.value
      });
    },
    
    // 执行搜索
    onSearch: function() {
      const that = this;
      
      if (!this.data.searchKeyword.trim()) {
        this.loadDiaryList(true);
        return;
      }
      
      this.setData({ loading: true });
      
      wx.request({
        url: 'http://localhost:5000/api/articles/search',
        method: 'GET',
        data: {
          title: this.data.searchKeyword
        },
        success: function(res) {
          if (res.data.success) {
            const searchResults = res.data.data.articles || [];
            that.setData({
              diaryList: searchResults.map(function(item) {
                return Object.assign({}, item, {
                  content_preview: that.getContentPreview(item.content || ''),
                  created_at: that.formatDate(item.created_at)
                });
              }),
              loading: false,
              hasMore: false
            });
          }
        },
        fail: function(error) {
          console.error('搜索失败:', error);
          wx.showToast({
            title: '搜索失败',
            icon: 'none'
          });
          that.setData({ loading: false });
        }
      });
    },
    
    // 筛选类型改变
    onFilterChange: function(e) {
      const filterType = e.currentTarget.dataset.type;
      this.setData({ filterType: filterType });
      this.loadDiaryList(true);
    },
    
    // 加载更多
    loadMore: function() {
      if (this.data.hasMore && !this.data.loading) {
        this.setData({
          currentPage: this.data.currentPage + 1
        });
        this.loadDiaryList(false);
      }
    },
    
    // 跳转到日记详情
    goToDiaryDetail: function(e) {
      const diaryId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/pages/diary-detail/diary-detail?id=' + diaryId
      });
    },
    
    // 跳转到创建日记
    goToCreateDiary: function() {
      wx.navigateTo({
        url: '/pages/diary-create/diary-create'
      });
    }
  }
});
